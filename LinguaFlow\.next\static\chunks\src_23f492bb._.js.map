{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-toast.ts"], "sourcesContent": ["\n\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 5000\n\nexport type ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n  titleKey?: string // For i18n\n  descriptionKey?: string // For i18n\n  descriptionParams?: Record<string, string | number> // For i18n params in description\n  titleParams?: Record<string, string | number> // For i18n params in title\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAGA,sCAAsC;AACtC;;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAa3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/config/languages.ts"], "sourcesContent": ["import { Flag } from \"lucide-react\";\n\nexport type ProficiencyLevel = 'native' | 'advanced' | 'intermediate' | 'beginner' | 'none';\n\nexport interface WritingLanguageInfo {\n  value: string; // Base language code e.g., \"en\", \"es\"\n  labelKey: string; // e.g., \"languageEnglishGeneral\"\n  dir: 'ltr' | 'rtl';\n  dialects?: Array<{ value: string; labelKey: string }>; // Full locale code e.g., \"en-US\"\n  supportsProficiency: boolean;\n}\n\nexport const APP_SUPPORTED_UI_LANGUAGES = [\n  { value: \"en-US\", labelKey: \"englishUSLanguage\", dir: \"ltr\" },\n  { value: \"en-GB\", labelKey: \"englishUKLanguage\", dir: \"ltr\" },\n  { value: \"ar\", labelKey: \"arabicLanguage\", dir: \"rtl\" },\n  { value: \"tr\", labelKey: \"turkishLanguage\", dir: \"ltr\" },\n  { value: \"es\", labelKey: \"spanishLanguage\", dir: \"ltr\" },\n  { value: \"de\", labelKey: \"germanLanguage\", dir: \"ltr\" },\n  { value: \"fr\", labelKey: \"frenchLanguage\", dir: \"ltr\" },\n  { value: \"nl\", labelKey: \"dutchLanguage\", dir: \"ltr\" },\n  { value: \"it\", labelKey: \"italianLanguage\", dir: \"ltr\" },\n];\n\nexport const APP_WRITING_LANGUAGES: WritingLanguageInfo[] = [\n  {\n    value: \"en\", labelKey: \"languageEnglishGeneral\", dir: \"ltr\",\n    dialects: [ { value: \"en-US\", labelKey: \"englishUSLanguage\" }, { value: \"en-GB\", labelKey: \"englishUKLanguage\" } ],\n    supportsProficiency: true,\n  },\n  {\n    value: \"es\", labelKey: \"languageSpanishGeneral\", dir: \"ltr\",\n    dialects: [ { value: \"es-ES\", labelKey: \"spanishSpainLanguage\" }, { value: \"es-MX\", labelKey: \"spanishMexicoLanguage\" } ],\n    supportsProficiency: true,\n  },\n  { value: \"fr\", labelKey: \"languageFrenchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"de\", labelKey: \"languageGermanGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"it\", labelKey: \"languageItalianGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"nl\", labelKey: \"languageDutchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  {\n    value: \"ar\", labelKey: \"languageArabicGeneral\", dir: \"rtl\",\n    dialects: [\n        { value: \"ar-SY\", labelKey: \"arabicSyriaLanguage\" },\n        { value: \"ar-SA\", labelKey: \"arabicSaudiArabiaLanguage\" },\n        { value: \"ar-EG\", labelKey: \"arabicEgyptLanguage\" },\n    ],\n    supportsProficiency: true,\n  },\n  { value: \"tr\", labelKey: \"languageTurkishGeneral\", dir: \"ltr\", supportsProficiency: true },\n];\n\nexport const PROFICIENCY_LEVELS: Array<{value: ProficiencyLevel, labelKey: string}> = [\n    {value: 'native', labelKey: 'proficiencyNative'},\n    {value: 'advanced', labelKey: 'proficiencyAdvanced'},\n    {value: 'intermediate', labelKey: 'proficiencyIntermediate'},\n    {value: 'beginner', labelKey: 'proficiencyBeginner'},\n];\n\n    "], "names": [], "mappings": ";;;;;AAYO,MAAM,6BAA6B;IACxC;QAAE,OAAO;QAAS,UAAU;QAAqB,KAAK;IAAM;IAC5D;QAAE,OAAO;QAAS,UAAU;QAAqB,KAAK;IAAM;IAC5D;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAiB,KAAK;IAAM;IACrD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;CACxD;AAEM,MAAM,wBAA+C;IAC1D;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YAAE;gBAAE,OAAO;gBAAS,UAAU;YAAoB;YAAG;gBAAE,OAAO;gBAAS,UAAU;YAAoB;SAAG;QAClH,qBAAqB;IACvB;IACA;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YAAE;gBAAE,OAAO;gBAAS,UAAU;YAAuB;YAAG;gBAAE,OAAO;gBAAS,UAAU;YAAwB;SAAG;QACzH,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;IACzF;QAAE,OAAO;QAAM,UAAU;QAAwB,KAAK;QAAO,qBAAqB;IAAK;IACvF;QACE,OAAO;QAAM,UAAU;QAAyB,KAAK;QACrD,UAAU;YACN;gBAAE,OAAO;gBAAS,UAAU;YAAsB;YAClD;gBAAE,OAAO;gBAAS,UAAU;YAA4B;YACxD;gBAAE,OAAO;gBAAS,UAAU;YAAsB;SACrD;QACD,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;CAC1F;AAEM,MAAM,qBAAyE;IAClF;QAAC,OAAO;QAAU,UAAU;IAAmB;IAC/C;QAAC,OAAO;QAAY,UAAU;IAAqB;IACnD;QAAC,OAAO;QAAgB,UAAU;IAAyB;IAC3D;QAAC,OAAO;QAAY,UAAU;IAAqB;CACtD", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/contexts/i18n-context.tsx"], "sourcesContent": ["\n'use client';\n\nimport type { ReactNode } from 'react';\nimport React, { createContext, useState, useEffect, useCallback, useContext } from 'react';\nimport { APP_SUPPORTED_UI_LANGUAGES, APP_WRITING_LANGUAGES, type ProficiencyLevel } from '@/config/languages';\n\n\nimport enUSTranslationsJson from '@/locales/en-US.json';\nimport enGBTranslationsJson from '@/locales/en-GB.json';\nimport arTranslationsJson from '@/locales/ar.json';\nimport trTranslationsJson from '@/locales/tr.json';\nimport esTranslationsJson from '@/locales/es.json';\nimport deTranslationsJson from '@/locales/de.json';\nimport frTranslationsJson from '@/locales/fr.json';\nimport nlTranslationsJson from '@/locales/nl.json';\nimport itTranslationsJson from '@/locales/it.json';\n\ntype Translations = Record<string, string>;\n\n\nconst getSafeTranslations = (json: any): Translations => {\n  if (json && typeof json === 'object' && Object.keys(json).length > 0) {\n    return json as Translations;\n  }\n  return {}; \n};\n\nconst allTranslationsData: Record<string, Translations> = {\n  'en-US': getSafeTranslations(enUSTranslationsJson),\n  'en-GB': getSafeTranslations(enGBTranslationsJson),\n  'ar': getSafeTranslations(arTranslationsJson),\n  'tr': getSafeTranslations(trTranslationsJson),\n  'es': getSafeTranslations(esTranslationsJson),\n  'de': getSafeTranslations(deTranslationsJson),\n  'fr': getSafeTranslations(frTranslationsJson),\n  'nl': getSafeTranslations(nlTranslationsJson),\n  'it': getSafeTranslations(itTranslationsJson),\n};\n\nconst DEFAULT_UI_LANGUAGE = 'en-US';\nconst DEFAULT_WRITING_LANGUAGE_DIALECT = 'en-US'; // Store full dialect\nconst DEFAULT_PROFICIENCY: ProficiencyLevel = 'native';\n\nexport interface I18nContextType {\n  uiLanguage: string;\n  setUiLanguage: (lang: string) => void;\n  writingLanguageDialect: string; // Stores full dialect e.g. \"en-US\", \"es-ES\"\n  setWritingLanguageDialect: (dialect: string) => void;\n  getWritingLanguageBase: () => string; // Helper to get \"en\" from \"en-US\"\n  languageProficiency: Record<string, ProficiencyLevel>; // Keyed by base language e.g. \"en\"\n  setLanguageProficiency: (baseLang: string, level: ProficiencyLevel) => void;\n  t: (key: string, params?: Record<string, string | number>) => string;\n}\n\nconst I18nContext = createContext<I18nContextType | undefined>(undefined);\n\nexport function I18nProvider({ children }: { children: ReactNode }) {\n  const [uiLanguage, setUiLanguageState] = useState<string>(DEFAULT_UI_LANGUAGE);\n  const [translations, setTranslations] = useState<Translations>(allTranslationsData[DEFAULT_UI_LANGUAGE]);\n  const [writingLanguageDialect, setWritingLanguageDialectState] = useState<string>(DEFAULT_WRITING_LANGUAGE_DIALECT);\n  const [languageProficiency, setLanguageProficiencyState] = useState<Record<string, ProficiencyLevel>>({});\n\n  const updateDocumentAttributes = (langCode: string) => {\n    const langInfo = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === langCode) || APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === DEFAULT_UI_LANGUAGE);\n    document.documentElement.lang = langCode;\n    document.documentElement.dir = langInfo?.dir || 'ltr';\n  };\n\n  useEffect(() => {\n    // Load UI Language\n    let storedUiLanguage = localStorage.getItem('lingua-flow-ui-language');\n    let detectedUiLang = DEFAULT_UI_LANGUAGE;\n\n    if (storedUiLanguage && allTranslationsData[storedUiLanguage] && Object.keys(allTranslationsData[storedUiLanguage]).length > 0) {\n      detectedUiLang = storedUiLanguage;\n    } else {\n      const browserLanguages = navigator.languages || [navigator.language];\n      for (const lang of browserLanguages) {\n        const normalizedLang = lang.replace('_', '-');\n        if (allTranslationsData[normalizedLang] && Object.keys(allTranslationsData[normalizedLang]).length > 0) {\n          detectedUiLang = normalizedLang;\n          break;\n        }\n        const baseLang = normalizedLang.split('-')[0];\n        const firstSupportedDialect = APP_SUPPORTED_UI_LANGUAGES.find(\n          (supported) => supported.value.startsWith(baseLang + '-') && allTranslationsData[supported.value] && Object.keys(allTranslationsData[supported.value]).length > 0\n        );\n        if (firstSupportedDialect) {\n          detectedUiLang = firstSupportedDialect.value;\n          break;\n        }\n        if (allTranslationsData[baseLang] && Object.keys(allTranslationsData[baseLang]).length > 0) {\n          detectedUiLang = baseLang;\n          break;\n        }\n      }\n    }\n     if (!allTranslationsData[detectedUiLang] || Object.keys(allTranslationsData[detectedUiLang]).length === 0) {\n        detectedUiLang = DEFAULT_UI_LANGUAGE;\n    }\n    setUiLanguageState(detectedUiLang);\n    setTranslations(allTranslationsData[detectedUiLang]);\n    if (localStorage.getItem('lingua-flow-ui-language') !== detectedUiLang) {\n      localStorage.setItem('lingua-flow-ui-language', detectedUiLang);\n    }\n    updateDocumentAttributes(detectedUiLang);\n\n    // Load Writing Language Dialect\n    const storedWritingLangDialect = localStorage.getItem('lingua-flow-writing-language-dialect');\n    setWritingLanguageDialectState(storedWritingLangDialect || DEFAULT_WRITING_LANGUAGE_DIALECT);\n\n    // Load Language Proficiency\n    const storedProficiency = localStorage.getItem('lingua-flow-language-proficiency');\n    if (storedProficiency) {\n      try {\n        setLanguageProficiencyState(JSON.parse(storedProficiency));\n      } catch (e) {\n        localStorage.removeItem('lingua-flow-language-proficiency'); // Clear if invalid\n      }\n    }\n  }, []);\n\n  const setUiLanguage = useCallback((lang: string) => {\n    if (allTranslationsData[lang] && Object.keys(allTranslationsData[lang]).length > 0) {\n      setUiLanguageState(lang);\n      setTranslations(allTranslationsData[lang]);\n      localStorage.setItem('lingua-flow-ui-language', lang);\n      updateDocumentAttributes(lang);\n    } else {\n      setUiLanguageState(DEFAULT_UI_LANGUAGE);\n      setTranslations(allTranslationsData[DEFAULT_UI_LANGUAGE]);\n      localStorage.setItem('lingua-flow-ui-language', DEFAULT_UI_LANGUAGE);\n      updateDocumentAttributes(DEFAULT_UI_LANGUAGE);\n    }\n  }, []);\n\n  const setWritingLanguageDialect = useCallback((dialect: string) => {\n    setWritingLanguageDialectState(dialect);\n    localStorage.setItem('lingua-flow-writing-language-dialect', dialect);\n  }, []);\n\n  const getWritingLanguageBase = useCallback((): string => {\n    return writingLanguageDialect.split('-')[0];\n  }, [writingLanguageDialect]);\n\n  const setLanguageProficiency = useCallback((baseLang: string, level: ProficiencyLevel) => {\n    setLanguageProficiencyState(prev => {\n      const newProficiency = { ...prev, [baseLang]: level };\n      localStorage.setItem('lingua-flow-language-proficiency', JSON.stringify(newProficiency));\n      return newProficiency;\n    });\n  }, []);\n\n  const t = useCallback((key: string, params?: Record<string, string | number>): string => {\n    let mappedString = translations[key] !== undefined ? translations[key] : key;\n    if (params) {\n      Object.keys(params).forEach(paramKey => {\n        mappedString = mappedString.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(params[paramKey]));\n      });\n    }\n    return mappedString;\n  }, [translations]);\n\n  return (\n    <I18nContext.Provider value={{ uiLanguage, setUiLanguage, writingLanguageDialect, setWritingLanguageDialect, getWritingLanguageBase, languageProficiency, setLanguageProficiency, t }}>\n      {children}\n    </I18nContext.Provider>\n  );\n}\n\nexport function useI18n(): I18nContextType {\n  const context = useContext(I18nContext);\n  if (context === undefined) {\n    throw new Error('useI18n must be used within an I18nProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAIA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;AAoBA,MAAM,sBAAsB,CAAC;IAC3B,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;QACpE,OAAO;IACT;IACA,OAAO,CAAC;AACV;AAEA,MAAM,sBAAoD;IACxD,SAAS,oBAAoB,kGAAA,CAAA,UAAoB;IACjD,SAAS,oBAAoB,kGAAA,CAAA,UAAoB;IACjD,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;AAC9C;AAEA,MAAM,sBAAsB;AAC5B,MAAM,mCAAmC,SAAS,qBAAqB;AACvE,MAAM,sBAAwC;AAa9C,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,YAAY,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,mBAAmB,CAAC,oBAAoB;IACvG,MAAM,CAAC,wBAAwB,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAClF,MAAM,CAAC,qBAAqB,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IAEvG,MAAM,2BAA2B,CAAC;QAChC,MAAM,WAAW,6HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,6HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAChI,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,UAAU,OAAO;IAClD;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,mBAAmB;YACnB,IAAI,mBAAmB,aAAa,OAAO,CAAC;YAC5C,IAAI,iBAAiB;YAErB,IAAI,oBAAoB,mBAAmB,CAAC,iBAAiB,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,MAAM,GAAG,GAAG;gBAC9H,iBAAiB;YACnB,OAAO;gBACL,MAAM,mBAAmB,UAAU,SAAS,IAAI;oBAAC,UAAU,QAAQ;iBAAC;gBACpE,KAAK,MAAM,QAAQ,iBAAkB;oBACnC,MAAM,iBAAiB,KAAK,OAAO,CAAC,KAAK;oBACzC,IAAI,mBAAmB,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,GAAG,GAAG;wBACtG,iBAAiB;wBACjB;oBACF;oBACA,MAAM,WAAW,eAAe,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC7C,MAAM,wBAAwB,6HAAA,CAAA,6BAA0B,CAAC,IAAI;wEAC3D,CAAC,YAAc,UAAU,KAAK,CAAC,UAAU,CAAC,WAAW,QAAQ,mBAAmB,CAAC,UAAU,KAAK,CAAC,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,KAAK,CAAC,EAAE,MAAM,GAAG;;oBAElK,IAAI,uBAAuB;wBACzB,iBAAiB,sBAAsB,KAAK;wBAC5C;oBACF;oBACA,IAAI,mBAAmB,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG;wBAC1F,iBAAiB;wBACjB;oBACF;gBACF;YACF;YACC,IAAI,CAAC,mBAAmB,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,KAAK,GAAG;gBACxG,iBAAiB;YACrB;YACA,mBAAmB;YACnB,gBAAgB,mBAAmB,CAAC,eAAe;YACnD,IAAI,aAAa,OAAO,CAAC,+BAA+B,gBAAgB;gBACtE,aAAa,OAAO,CAAC,2BAA2B;YAClD;YACA,yBAAyB;YAEzB,gCAAgC;YAChC,MAAM,2BAA2B,aAAa,OAAO,CAAC;YACtD,+BAA+B,4BAA4B;YAE3D,4BAA4B;YAC5B,MAAM,oBAAoB,aAAa,OAAO,CAAC;YAC/C,IAAI,mBAAmB;gBACrB,IAAI;oBACF,4BAA4B,KAAK,KAAK,CAAC;gBACzC,EAAE,OAAO,GAAG;oBACV,aAAa,UAAU,CAAC,qCAAqC,mBAAmB;gBAClF;YACF;QACF;iCAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,IAAI,mBAAmB,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG;gBAClF,mBAAmB;gBACnB,gBAAgB,mBAAmB,CAAC,KAAK;gBACzC,aAAa,OAAO,CAAC,2BAA2B;gBAChD,yBAAyB;YAC3B,OAAO;gBACL,mBAAmB;gBACnB,gBAAgB,mBAAmB,CAAC,oBAAoB;gBACxD,aAAa,OAAO,CAAC,2BAA2B;gBAChD,yBAAyB;YAC3B;QACF;kDAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YAC7C,+BAA+B;YAC/B,aAAa,OAAO,CAAC,wCAAwC;QAC/D;8DAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACzC,OAAO,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7C;2DAAG;QAAC;KAAuB;IAE3B,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,UAAkB;YAC5D;oEAA4B,CAAA;oBAC1B,MAAM,iBAAiB;wBAAE,GAAG,IAAI;wBAAE,CAAC,SAAS,EAAE;oBAAM;oBACpD,aAAa,OAAO,CAAC,oCAAoC,KAAK,SAAS,CAAC;oBACxE,OAAO;gBACT;;QACF;2DAAG,EAAE;IAEL,MAAM,IAAI,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,CAAC,KAAa;YAClC,IAAI,eAAe,YAAY,CAAC,IAAI,KAAK,YAAY,YAAY,CAAC,IAAI,GAAG;YACzE,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,QAAQ,OAAO;mDAAC,CAAA;wBAC1B,eAAe,aAAa,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,OAAO,MAAM,CAAC,SAAS;oBACjG;;YACF;YACA,OAAO;QACT;sCAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAY;YAAe;YAAwB;YAA2B;YAAwB;YAAqB;YAAwB;QAAE;kBACjL;;;;;;AAGP;GAhHgB;KAAA;AAkHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toaster.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { useToast, type ToasterToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\nimport { useI18n } from \"@/contexts/i18n-context\"\n\nexport function Toaster() {\n  const { toasts } = useToast();\n  const { t } = useI18n();\n\n  return (\n    <ToastProvider>\n      {toasts.map(function (toastProps: ToasterToast) {\n        const { id, title, description, action, titleKey, descriptionKey, titleParams, descriptionParams, ...props } = toastProps;\n        \n        const toastTitle = titleKey ? t(titleKey, titleParams) : title;\n        const toastDescription = descriptionKey ? t(descriptionKey, descriptionParams) : description;\n\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {toastTitle && <ToastTitle>{toastTitle}</ToastTitle>}\n              {toastDescription && (\n                <ToastDescription>{toastDescription}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAQA;;;AAXA;;;;AAaO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC1B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,UAAwB;gBAC5C,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,OAAO,GAAG;gBAE/G,MAAM,aAAa,WAAW,EAAE,UAAU,eAAe;gBACzD,MAAM,mBAAmB,iBAAiB,EAAE,gBAAgB,qBAAqB;gBAEjF,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,4BAAc,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCAC3B,kCACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GA5BgB;;QACK,+HAAA,CAAA,WAAQ;QACb,sIAAA,CAAA,UAAO;;;KAFP", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/contexts/theme-context.tsx"], "sourcesContent": ["\n'use client';\n\nimport type { ReactNode } from 'react';\nimport { createContext, useState, useEffect, useCallback, useContext } from 'react';\n\nexport type ThemeSetting = 'light' | 'dark' | 'system';\nexport type FontSizeSetting = 'small' | 'medium' | 'large';\n\ninterface AppearanceContextType {\n  theme: ThemeSetting;\n  setTheme: (theme: ThemeSetting) => void;\n  fontSize: FontSizeSetting;\n  setFontSize: (size: FontSizeSetting) => void;\n  isHighContrast: boolean;\n  toggleHighContrast: () => void;\n  effectiveTheme: 'light' | 'dark'; // Actual theme being applied (resolves 'system')\n}\n\nconst AppearanceContext = createContext<AppearanceContextType | undefined>(undefined);\n\nconst FONT_SIZE_PX_MAP: Record<FontSizeSetting, string> = {\n  small: '14px',\n  medium: '16px',\n  large: '18px',\n};\n\nconst DEFAULT_FONT_SIZE: FontSizeSetting = 'medium';\nconst DEFAULT_THEME: ThemeSetting = 'system';\n\nexport function AppearanceProvider({ children }: { children: ReactNode }) {\n  const [theme, setThemeState] = useState<ThemeSetting>(DEFAULT_THEME);\n  const [fontSize, setFontSizeState] = useState<FontSizeSetting>(DEFAULT_FONT_SIZE);\n  const [isHighContrast, setIsHighContrastState] = useState<boolean>(false);\n  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>('light');\n\n  const applyTheme = useCallback((newTheme: ThemeSetting) => {\n    let currentEffectiveTheme: 'light' | 'dark';\n    if (newTheme === 'system') {\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      currentEffectiveTheme = systemPrefersDark ? 'dark' : 'light';\n    } else {\n      currentEffectiveTheme = newTheme;\n    }\n\n    setEffectiveTheme(currentEffectiveTheme);\n    document.documentElement.classList.remove('light', 'dark');\n    document.documentElement.classList.add(currentEffectiveTheme);\n    localStorage.setItem('lingua-flow-theme', newTheme);\n    setThemeState(newTheme);\n  }, []);\n\n  const applyFontSize = useCallback((newSize: FontSizeSetting) => {\n    document.documentElement.style.fontSize = FONT_SIZE_PX_MAP[newSize];\n    localStorage.setItem('lingua-flow-font-size', newSize);\n    setFontSizeState(newSize);\n  }, []);\n\n  const applyHighContrast = useCallback((newHighContrastState: boolean) => {\n    if (newHighContrastState) {\n      document.documentElement.classList.add('high-contrast');\n    } else {\n      document.documentElement.classList.remove('high-contrast');\n    }\n    localStorage.setItem('lingua-flow-high-contrast', JSON.stringify(newHighContrastState));\n    setIsHighContrastState(newHighContrastState);\n  }, []);\n  \n  useEffect(() => {\n    const storedTheme = localStorage.getItem('lingua-flow-theme') as ThemeSetting | null;\n    applyTheme(storedTheme || DEFAULT_THEME);\n\n    const storedFontSize = localStorage.getItem('lingua-flow-font-size') as FontSizeSetting | null;\n    applyFontSize(storedFontSize || DEFAULT_FONT_SIZE);\n\n    const storedHighContrast = localStorage.getItem('lingua-flow-high-contrast');\n    applyHighContrast(storedHighContrast ? JSON.parse(storedHighContrast) : false);\n  }, [applyTheme, applyFontSize, applyHighContrast]);\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        applyTheme('system'); // Re-apply to reflect system change\n      }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme, applyTheme]);\n\n  const setTheme = useCallback((newTheme: ThemeSetting) => {\n    applyTheme(newTheme);\n  }, [applyTheme]);\n\n  const setFontSize = useCallback((newSize: FontSizeSetting) => {\n    applyFontSize(newSize);\n  }, [applyFontSize]);\n\n  const toggleHighContrast = useCallback(() => {\n    applyHighContrast(!isHighContrast);\n  }, [isHighContrast, applyHighContrast]);\n\n  return (\n    <AppearanceContext.Provider value={{ theme, setTheme, fontSize, setFontSize, isHighContrast, toggleHighContrast, effectiveTheme }}>\n      {children}\n    </AppearanceContext.Provider>\n  );\n}\n\nexport function useAppearance(): AppearanceContextType {\n  const context = useContext(AppearanceContext);\n  if (context === undefined) {\n    throw new Error('useAppearance must be used within an AppearanceProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAIA;;;AAHA;;AAkBA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAqC;AAE3E,MAAM,mBAAoD;IACxD,OAAO;IACP,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,oBAAqC;AAC3C,MAAM,gBAA8B;AAE7B,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACtD,MAAM,CAAC,UAAU,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC/D,MAAM,CAAC,gBAAgB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAC9B,IAAI;YACJ,IAAI,aAAa,UAAU;gBACzB,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,wBAAwB,oBAAoB,SAAS;YACvD,OAAO;gBACL,wBAAwB;YAC1B;YAEA,kBAAkB;YAClB,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS;YACnD,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,qBAAqB;YAC1C,cAAc;QAChB;qDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACjC,SAAS,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ;YACnE,aAAa,OAAO,CAAC,yBAAyB;YAC9C,iBAAiB;QACnB;wDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACrC,IAAI,sBAAsB;gBACxB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;YACA,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YACjE,uBAAuB;QACzB;4DAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,WAAW,eAAe;YAE1B,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,cAAc,kBAAkB;YAEhC,MAAM,qBAAqB,aAAa,OAAO,CAAC;YAChD,kBAAkB,qBAAqB,KAAK,KAAK,CAAC,sBAAsB;QAC1E;uCAAG;QAAC;QAAY;QAAe;KAAkB;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;6DAAe;oBACnB,IAAI,UAAU,UAAU;wBACtB,WAAW,WAAW,oCAAoC;oBAC5D;gBACF;;YACA,WAAW,gBAAgB,CAAC,UAAU;YACtC;gDAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;uCAAG;QAAC;QAAO;KAAW;IAEtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAC5B,WAAW;QACb;mDAAG;QAAC;KAAW;IAEf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAC/B,cAAc;QAChB;sDAAG;QAAC;KAAc;IAElB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACrC,kBAAkB,CAAC;QACrB;6DAAG;QAAC;QAAgB;KAAkB;IAEtC,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;YAAU;YAAa;YAAgB;YAAoB;QAAe;kBAC7H;;;;;;AAGP;GA7EgB;KAAA;AA+ET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/contexts/dictionary-context.tsx"], "sourcesContent": ["\n'use client';\n\nimport type { ReactNode } from 'react';\nimport React, { createContext, useState, useEffect, useCallback, useContext } from 'react';\nimport { useToast } from '@/hooks/use-toast';\n\ntype DictionaryWords = Record<string, string[]>;\n\ninterface DictionaryContextType {\n  words: DictionaryWords;\n  addWord: (word: string, lang: string) => boolean;\n  deleteWord: (word: string, lang: string) => void;\n  importWords: (newWords: string[], lang: string, overwrite: boolean) => void;\n  exportWordsString: (lang: string) => string;\n  clearDictionary: (lang: string) => void;\n  isWordInDictionary: (word: string, lang: string) => boolean;\n  getWordsForLanguage: (lang: string) => string[];\n}\n\nconst DictionaryContext = createContext<DictionaryContextType | undefined>(undefined);\n\nconst DICTIONARY_STORAGE_KEY = 'lingua-flow-dictionary';\n\nexport function DictionaryProvider({ children }: { children: ReactNode }) {\n  const [words, setWords] = useState<DictionaryWords>({});\n  const { toast } = useToast();\n\n  useEffect(() => {\n    const storedWords = localStorage.getItem(DICTIONARY_STORAGE_KEY);\n    if (storedWords) {\n      try {\n        let parsedWords = JSON.parse(storedWords);\n        \n        if (Array.isArray(parsedWords)) {\n          parsedWords = { 'en': parsedWords }; \n          localStorage.setItem(DICTIONARY_STORAGE_KEY, JSON.stringify(parsedWords));\n        }\n\n        if (typeof parsedWords === 'object' && !Array.isArray(parsedWords) && parsedWords !== null) {\n          Object.keys(parsedWords).forEach(lang => {\n            if (Array.isArray(parsedWords[lang])) {\n              parsedWords[lang].sort((a,b) => a.localeCompare(b));\n            }\n          });\n          setWords(parsedWords);\n        }\n      } catch (e) {\n        console.error(\"Error parsing dictionary from localStorage\", e);\n        localStorage.removeItem(DICTIONARY_STORAGE_KEY);\n      }\n    }\n  }, []);\n\n  const persistWords = useCallback((newWords: DictionaryWords) => {\n    localStorage.setItem(DICTIONARY_STORAGE_KEY, JSON.stringify(newWords));\n    setWords(newWords);\n  }, []);\n\n  const addWord = useCallback((word: string, lang: string): boolean => {\n    const trimmedWord = word.trim();\n    if (!trimmedWord) {\n        toast({ titleKey: \"toastDictionaryWordEmpty\", variant: \"destructive\" });\n        return false;\n    }\n    const currentLangWords = words[lang] || [];\n    if (currentLangWords.some(w => w.toLowerCase() === trimmedWord.toLowerCase())) {\n        toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastDictionaryWordExists\", descriptionParams: { word: trimmedWord } });\n        return false;\n    }\n    \n    const newLangWords = [...currentLangWords, trimmedWord].sort((a,b) => a.localeCompare(b));\n    persistWords({ ...words, [lang]: newLangWords });\n    toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryWordAdded\", descriptionParams: { word: trimmedWord } });\n    return true;\n  }, [words, persistWords, toast]);\n\n  const deleteWord = useCallback((wordToDelete: string, lang: string) => {\n    const currentLangWords = words[lang] || [];\n    const newLangWords = currentLangWords.filter(w => w.toLowerCase() !== wordToDelete.toLowerCase());\n    persistWords({ ...words, [lang]: newLangWords });\n    toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryWordDeleted\", descriptionParams: { word: wordToDelete } });\n  }, [words, persistWords, toast]);\n\n  const importWords = useCallback((newWordsToImport: string[], lang: string, overwrite: boolean) => {\n    const uniqueNewWords = Array.from(new Set(newWordsToImport.map(w => w.trim()).filter(Boolean)));\n    const currentLangWords = words[lang] || [];\n    \n    if (overwrite) {\n      const newLangWords = uniqueNewWords.sort((a,b) => a.localeCompare(b));\n      persistWords({ ...words, [lang]: newLangWords });\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryImportOverwriteSuccess\", descriptionParams: { count: newLangWords.length }});\n    } else {\n      const combined = Array.from(new Set([...currentLangWords, ...uniqueNewWords])).sort((a,b) => a.localeCompare(b));\n      persistWords({ ...words, [lang]: combined });\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryImportMergeSuccess\", descriptionParams: { count: combined.length - currentLangWords.length }});\n    }\n  }, [words, persistWords, toast]);\n\n  const exportWordsString = useCallback((lang: string): string => {\n    return JSON.stringify(words[lang] || [], null, 2);\n  }, [words]);\n\n  const clearDictionary = useCallback((lang: string) => {\n    const newWords = { ...words };\n    delete newWords[lang];\n    persistWords(newWords);\n    toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryCleared\" });\n  }, [words, persistWords, toast]);\n\n  const isWordInDictionary = useCallback((word: string, lang: string): boolean => {\n    const langWords = words[lang] || [];\n    return langWords.some(w => w.toLowerCase() === word.trim().toLowerCase());\n  }, [words]);\n\n  const getWordsForLanguage = useCallback((lang: string): string[] => {\n    return words[lang] || [];\n  }, [words]);\n\n  return (\n    <DictionaryContext.Provider value={{ words, addWord, deleteWord, importWords, exportWordsString, clearDictionary, isWordInDictionary, getWordsForLanguage }}>\n      {children}\n    </DictionaryContext.Provider>\n  );\n}\n\nexport function useDictionary(): DictionaryContextType {\n  const context = useContext(DictionaryContext);\n  if (context === undefined) {\n    throw new Error('useDictionary must be used within a DictionaryProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAIA;AACA;;;AAJA;;;AAmBA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAqC;AAE3E,MAAM,yBAAyB;AAExB,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IACrD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,IAAI;oBACF,IAAI,cAAc,KAAK,KAAK,CAAC;oBAE7B,IAAI,MAAM,OAAO,CAAC,cAAc;wBAC9B,cAAc;4BAAE,MAAM;wBAAY;wBAClC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;oBAC9D;oBAEA,IAAI,OAAO,gBAAgB,YAAY,CAAC,MAAM,OAAO,CAAC,gBAAgB,gBAAgB,MAAM;wBAC1F,OAAO,IAAI,CAAC,aAAa,OAAO;4DAAC,CAAA;gCAC/B,IAAI,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,GAAG;oCACpC,WAAW,CAAC,KAAK,CAAC,IAAI;wEAAC,CAAC,GAAE,IAAM,EAAE,aAAa,CAAC;;gCAClD;4BACF;;wBACA,SAAS;oBACX;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,8CAA8C;oBAC5D,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;uCAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC5D,SAAS;QACX;uDAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,MAAc;YACzC,MAAM,cAAc,KAAK,IAAI;YAC7B,IAAI,CAAC,aAAa;gBACd,MAAM;oBAAE,UAAU;oBAA4B,SAAS;gBAAc;gBACrE,OAAO;YACX;YACA,MAAM,mBAAmB,KAAK,CAAC,KAAK,IAAI,EAAE;YAC1C,IAAI,iBAAiB,IAAI;2DAAC,CAAA,IAAK,EAAE,WAAW,OAAO,YAAY,WAAW;2DAAK;gBAC3E,MAAM;oBAAE,UAAU;oBAAkB,gBAAgB;oBAA6B,mBAAmB;wBAAE,MAAM;oBAAY;gBAAE;gBAC1H,OAAO;YACX;YAEA,MAAM,eAAe;mBAAI;gBAAkB;aAAY,CAAC,IAAI;wEAAC,CAAC,GAAE,IAAM,EAAE,aAAa,CAAC;;YACtF,aAAa;gBAAE,GAAG,KAAK;gBAAE,CAAC,KAAK,EAAE;YAAa;YAC9C,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;gBAA4B,mBAAmB;oBAAE,MAAM;gBAAY;YAAE;YAC5H,OAAO;QACT;kDAAG;QAAC;QAAO;QAAc;KAAM;IAE/B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,cAAsB;YACpD,MAAM,mBAAmB,KAAK,CAAC,KAAK,IAAI,EAAE;YAC1C,MAAM,eAAe,iBAAiB,MAAM;2EAAC,CAAA,IAAK,EAAE,WAAW,OAAO,aAAa,WAAW;;YAC9F,aAAa;gBAAE,GAAG,KAAK;gBAAE,CAAC,KAAK,EAAE;YAAa;YAC9C,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;gBAA8B,mBAAmB;oBAAE,MAAM;gBAAa;YAAE;QACjI;qDAAG;QAAC;QAAO;QAAc;KAAM;IAE/B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,kBAA4B,MAAc;YACzE,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,GAAG;8EAAC,CAAA,IAAK,EAAE,IAAI;6EAAI,MAAM,CAAC;YACrF,MAAM,mBAAmB,KAAK,CAAC,KAAK,IAAI,EAAE;YAE1C,IAAI,WAAW;gBACb,MAAM,eAAe,eAAe,IAAI;gFAAC,CAAC,GAAE,IAAM,EAAE,aAAa,CAAC;;gBAClE,aAAa;oBAAE,GAAG,KAAK;oBAAE,CAAC,KAAK,EAAE;gBAAa;gBAC9C,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;oBAAyC,mBAAmB;wBAAE,OAAO,aAAa,MAAM;oBAAC;gBAAC;YACnJ,OAAO;gBACL,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,IAAI;uBAAI;uBAAqB;iBAAe,GAAG,IAAI;4EAAC,CAAC,GAAE,IAAM,EAAE,aAAa,CAAC;;gBAC7G,aAAa;oBAAE,GAAG,KAAK;oBAAE,CAAC,KAAK,EAAE;gBAAS;gBAC1C,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;oBAAqC,mBAAmB;wBAAE,OAAO,SAAS,MAAM,GAAG,iBAAiB,MAAM;oBAAC;gBAAC;YACrK;QACF;sDAAG;QAAC;QAAO;QAAc;KAAM;IAE/B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACrC,OAAO,KAAK,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE,MAAM;QACjD;4DAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACnC,MAAM,WAAW;gBAAE,GAAG,KAAK;YAAC;YAC5B,OAAO,QAAQ,CAAC,KAAK;YACrB,aAAa;YACb,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAyB;QAClF;0DAAG;QAAC;QAAO;QAAc;KAAM;IAE/B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC,MAAc;YACpD,MAAM,YAAY,KAAK,CAAC,KAAK,IAAI,EAAE;YACnC,OAAO,UAAU,IAAI;sEAAC,CAAA,IAAK,EAAE,WAAW,OAAO,KAAK,IAAI,GAAG,WAAW;;QACxE;6DAAG;QAAC;KAAM;IAEV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YACvC,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;QAC1B;8DAAG;QAAC;KAAM;IAEV,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAS;YAAY;YAAa;YAAmB;YAAiB;YAAoB;QAAoB;kBACvJ;;;;;;AAGP;GApGgB;;QAEI,+HAAA,CAAA,WAAQ;;;KAFZ;AAsGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/contexts/feature-settings-context.tsx"], "sourcesContent": ["\n'use client';\n\nimport type { ReactNode } from 'react';\nimport React, { createContext, useState, useEffect, useCallback, useContext } from 'react';\n\n// Define the shape of the feature settings state\nexport interface FeatureSettings {\n  // Generative AI Features\n  showAiSuggestionsOnSelection: boolean;\n  quickAiActions: boolean;\n  aiQuickReplies: boolean; // Mocked\n  promptHistory: boolean;\n\n  // Auto-Correction Features\n  autoCorrect: boolean;\n  realTimeCorrection: boolean;\n  sentenceEnhancement: boolean;\n  showCorrectionFeedback: boolean;\n\n  // Core Checking Features\n  realTimeGrammarCheck: boolean;\n  realTimeSpellCheck: boolean;\n  styleSuggestions: boolean;\n\n  // Writing Aid Features\n  enableToneDetection: boolean;\n  enablePlagiarismDetection: boolean;\n  enableNonNativeSpeakerSupport: boolean;\n\n  // Advanced Features\n  enableOfflineFunctionality: boolean;\n  enableAutomaticLanguageDetection: boolean;\n}\n\n// Define the default values for the features\nexport const defaultFeatureSettings: FeatureSettings = {\n  showAiSuggestionsOnSelection: true,\n  quickAiActions: true,\n  aiQuickReplies: false, // Mocked, so off by default\n  promptHistory: true,\n\n  autoCorrect: false, // Off by default as it can be intrusive\n  realTimeCorrection: true,\n  sentenceEnhancement: true,\n  showCorrectionFeedback: true,\n\n  realTimeGrammarCheck: true,\n  realTimeSpellCheck: true,\n  styleSuggestions: true,\n\n  // Writing Aid Features\n  enableToneDetection: true,\n  enablePlagiarismDetection: true,\n  enableNonNativeSpeakerSupport: false, // Off by default as it's specialized\n\n  // Advanced Features\n  enableOfflineFunctionality: false, // Off by default\n  enableAutomaticLanguageDetection: true,\n};\n\ninterface FeatureSettingsContextType {\n  settings: FeatureSettings;\n  setFeatureSetting: <K extends keyof FeatureSettings>(key: K, value: FeatureSettings[K]) => void;\n}\n\nconst FeatureSettingsContext = createContext<FeatureSettingsContextType | undefined>(undefined);\n\nconst SETTINGS_STORAGE_KEY = 'lingua-flow-feature-settings';\n\nexport function FeatureSettingsProvider({ children }: { children: ReactNode }) {\n  const [settings, setSettings] = useState<FeatureSettings>(defaultFeatureSettings);\n\n  useEffect(() => {\n    const storedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);\n    if (storedSettings) {\n      try {\n        const parsedSettings = JSON.parse(storedSettings);\n        // Merge with defaults to ensure all keys are present, even if new ones are added later\n        setSettings({ ...defaultFeatureSettings, ...parsedSettings });\n      } catch (e) {\n        console.error(\"Error parsing feature settings from localStorage\", e);\n        // If parsing fails, stick with defaults and remove the broken item\n        localStorage.removeItem(SETTINGS_STORAGE_KEY);\n      }\n    }\n  }, []);\n\n  const setFeatureSetting = useCallback(<K extends keyof FeatureSettings>(key: K, value: FeatureSettings[K]) => {\n    setSettings(prevSettings => {\n      const newSettings = { ...prevSettings, [key]: value };\n      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newSettings));\n      return newSettings;\n    });\n  }, []);\n\n  return (\n    <FeatureSettingsContext.Provider value={{ settings, setFeatureSetting }}>\n      {children}\n    </FeatureSettingsContext.Provider>\n  );\n}\n\nexport function useFeatureSettings(): FeatureSettingsContextType {\n  const context = useContext(FeatureSettingsContext);\n  if (context === undefined) {\n    throw new Error('useFeatureSettings must be used within a FeatureSettingsProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAIA;;;AAHA;;AAmCO,MAAM,yBAA0C;IACrD,8BAA8B;IAC9B,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IAEf,aAAa;IACb,oBAAoB;IACpB,qBAAqB;IACrB,wBAAwB;IAExB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAElB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAE/B,oBAAoB;IACpB,4BAA4B;IAC5B,kCAAkC;AACpC;AAOA,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA0C;AAErF,MAAM,uBAAuB;AAEtB,SAAS,wBAAwB,EAAE,QAAQ,EAA2B;;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,IAAI;oBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC;oBAClC,uFAAuF;oBACvF,YAAY;wBAAE,GAAG,sBAAsB;wBAAE,GAAG,cAAc;oBAAC;gBAC7D,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,oDAAoD;oBAClE,mEAAmE;oBACnE,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;4CAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,CAAkC,KAAQ;YAC9E;0EAAY,CAAA;oBACV,MAAM,cAAc;wBAAE,GAAG,YAAY;wBAAE,CAAC,IAAI,EAAE;oBAAM;oBACpD,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;oBAC1D,OAAO;gBACT;;QACF;iEAAG,EAAE;IAEL,qBACE,6LAAC,uBAAuB,QAAQ;QAAC,OAAO;YAAE;YAAU;QAAkB;kBACnE;;;;;;AAGP;GA/BgB;KAAA;AAiCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}