
"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { PopoverContent } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Button } from "@/components/ui/button";
import { Loader2, Volume2 } from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';
import { useToast } from '@/hooks/use-toast';
// Removed direct imports of server actions - now using API routes
type WordToolkitOutput = {
  synonyms: string[];
  definitions: string[];
  pronunciation: string;
  examples: string[];
};

type WordToolkitInput = {
  word: string;
  context: string;
  language: string;
};

type SpelledOutAudioInput = {
  text: string;
  language: string;
  voice: string;
};

interface WordToolkitPopoverProps {
  selectedWord: string;
  contextText: string;
  language: string;
  onSynonymSelect: (synonym: string) => void;
}

export function WordToolkitPopover({ selectedWord, contextText, language, onSynonymSelect }: WordToolkitPopoverProps) {
  const { t } = useI18n();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [analysisResult, setAnalysisResult] = useState<WordToolkitOutput | null>(null);
  const [isPronouncing, setIsPronouncing] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const handleWordAnalysis = useCallback(async () => {
    setIsLoading(true);
    setAnalysisResult(null);
    try {
      const input: WordToolkitInput = {
        word: selectedWord,
        context: contextText,
        language,
      };
      const response = await fetch('/api/ai/word-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setAnalysisResult(result);
    } catch (error) {
      console.error("Error in word toolkit analysis:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastWordToolkitError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [selectedWord, contextText, language, toast]);

  useEffect(() => {
    if (selectedWord) {
        handleWordAnalysis();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedWord, contextText, language]);

  const handlePronounce = async () => {
    if (!analysisResult?.correctSpelling) return;
    setIsPronouncing(true);
    try {
      const input: SpelledOutAudioInput = {
        word: analysisResult.correctSpelling,
        lang: language,
      };
      const response = await fetch('/api/ai/text-to-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      const { audioDataUri } = result;

      if (audioDataUri && audioRef.current) {
        audioRef.current.src = audioDataUri;
        audioRef.current.play();
      } else {
        toast({ titleKey: "toastErrorTitle", descriptionKey: "toastPronunciationError", variant: "destructive" });
      }
    } catch (error) {
      console.error("Error generating or playing pronunciation:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastPronunciationError", variant: "destructive" });
    } finally {
      setIsPronouncing(false);
    }
  };

  return (
    <PopoverContent className="w-80" side="top" align="start">
      <div className="grid gap-4">
        <div className="space-y-2">
          <h4 className="font-medium leading-none">{t('wordToolkitTitle')}</h4>
          <p className="text-sm text-muted-foreground">{t('wordToolkitPopoverDescription')}</p>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center h-24">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
          </div>
        )}

        {!isLoading && analysisResult && (
          <div className="space-y-4">
            <div>
              <h5 className="text-sm font-semibold mb-2">{t('synonymsLabel')}</h5>
              <div className="flex flex-wrap gap-2">
                {analysisResult.synonyms.length > 0 ? (
                  analysisResult.synonyms.map((synonym) => (
                    <Badge
                      key={synonym}
                      variant="secondary"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                      onClick={() => onSynonymSelect(synonym)}
                      title={t('applySynonymTooltip', { synonym })}
                    >
                      {synonym}
                    </Badge>
                  ))
                ) : (
                  <p className="text-xs text-muted-foreground">{t('noSynonymsFound')}</p>
                )}
              </div>
            </div>

            <div>
              <h5 className="text-sm font-semibold mb-2">{t('pronunciationLabel')}</h5>
              <div className="flex items-center justify-between gap-2 p-2 bg-muted rounded-md">
                <span className="text-sm font-semibold">{analysisResult.correctSpelling}</span>
                <Button size="icon" variant="ghost" className="h-7 w-7" onClick={handlePronounce} disabled={isPronouncing} title={t('pronounceButton')}>
                  {isPronouncing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Volume2 className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      <audio ref={audioRef} className="hidden" onEnded={() => setIsPronouncing(false)} onError={() => setIsPronouncing(false)}/>
    </PopoverContent>
  );
}
